/* 优化后的CSS代码 */
.item-thumbnail {
  position: relative;
  overflow: hidden;
  transition: transform 0.5s cubic-bezier(0.2, 0.8, 0.2, 1), 
              box-shadow 0.5s ease;
  border-radius: 8px;
}

.item-thumbnail:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* 彩色边框动画 */
.item-thumbnail::before {
  content: '';
  position: absolute;
  inset: -3px;
  background: linear-gradient(45deg, #ff00cc, #3300ff, #00ccff, #ff00cc);
  background-size: 400% 400%;
  z-index: -1;
  border-radius: 10px;
  opacity: 0;
  transition: opacity 0.5s ease;
  animation: gradientBG 6s ease infinite;
}

.item-thumbnail:hover::before {
  opacity: 1;
}

/* 渐变覆盖层 */
.item-thumbnail::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, 
              rgba(66, 0, 255, 0.2), 
              rgba(255, 0, 140, 0.2));
  transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
  border-radius: 8px;
  opacity: 1;
  backdrop-filter: blur(0px);
}

.item-thumbnail:hover::after {
  background: linear-gradient(135deg, 
              rgba(66, 0, 255, 0.6), 
              rgba(255, 0, 140, 0.4));
  backdrop-filter: blur(3px);
  box-shadow: inset 0 0 30px rgba(255, 255, 255, 0.2);
}

/* Logo图标 - 改为独立元素 */
.item-thumbnail .logo {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  margin: -30px 0 0 -30px;
  background: url(https://www.ranshare.com/wp-content/uploads/2025/07/20250718163530599-ranshare-resource-share-platform-favicon.svg) 
              no-repeat center/contain;
  z-index: 3;
  transform: scale(0) translateY(20px);
  transition: all 0.7s cubic-bezier(0.19, 1, 0.22, 1);
  opacity: 0;
  pointer-events: none;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
}

.item-thumbnail:hover .logo {
  transform: scale(1) translateY(0);
  opacity: 1;
  animation: float 3s infinite ease-in-out, 
             glow 2s infinite alternate;
}

/* 动画优化 */
@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-10px) rotate(5deg) scale(1.05);
  }
}

@keyframes glow {
  0% {
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
  }
  100% {
    filter: drop-shadow(0 0 20px #fff) 
            drop-shadow(0 0 30px rgba(66, 0, 255, 0.6));
  }
}

@keyframes gradientBG {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}
